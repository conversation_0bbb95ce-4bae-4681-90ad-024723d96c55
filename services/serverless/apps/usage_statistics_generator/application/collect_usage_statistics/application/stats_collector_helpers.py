from typing import Sequence, cast

import numpy as np
import pandas as pd

from services.base.domain.schemas.events.document_base import Document


async def get_documents_data_into_df(
    documents: Sequence[Document],
    fields: Sequence[str],
) -> pd.DataFrame:
    """
    Returns pandas dataframe, that contains one datatype object per row with columns specified in
    the field parameter.
    """

    def get_field_data(input_doc, field):
        for field in field.split("."):
            input_doc = getattr(input_doc, field)
        return input_doc

    return_df = pd.DataFrame(columns=[field.split(".")[-1] for field in fields])
    for document in documents:
        temp_df = pd.DataFrame(
            {field.split(".")[-1]: get_field_data(document, field) for field in fields}, index=range(1)
        )
        return_df = pd.concat([return_df, temp_df]).reset_index(drop=True)

    return return_df.fillna(value=np.nan)
