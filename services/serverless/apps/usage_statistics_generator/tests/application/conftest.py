from typing import Sequence

import pytest

from services.base.domain.schemas.events.event import Event
from services.base.tests.domain.builders.event_builder import EventBuilder
from services.serverless.apps.usage_statistics_generator.application.collect_usage_statistics.application.stats_collector import (
    StatsCollector,
)


@pytest.fixture()
def collector(dependency_bootstrapper) -> StatsCollector:
    return dependency_bootstrapper.get(StatsCollector)


@pytest.fixture()
def events_sample() -> Sequence[Event]:
    return EventBuilder().build_n(n=10)
