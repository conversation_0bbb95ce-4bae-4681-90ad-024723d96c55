from typing import Any, Dict

from opensearchpy import Date, Double, Keyword, Object, Text

from services.base.domain.constants.document_labels import Document<PERSON>abels
from services.base.domain.schemas.events.event import EventFields, EventValueLimits
from services.base.domain.schemas.templates.payload.nutrition_collection_template_payloads import (
    NutritionTemplateFields,
)
from services.base.domain.schemas.templates.template import TemplateFields
from services.base.infrastructure.database.opensearch.index_settings.activity import activity_mapping
from services.base.infrastructure.database.opensearch.index_settings.body_metric import body_metric_mapping
from services.base.infrastructure.database.opensearch.index_settings.content import content_mapping
from services.base.infrastructure.database.opensearch.index_settings.exercise import exercise_mapping
from services.base.infrastructure.database.opensearch.index_settings.medication import medication_mapping
from services.base.infrastructure.database.opensearch.index_settings.note import note_mapping
from services.base.infrastructure.database.opensearch.index_settings.nutrition import nutrition_mapping
from services.base.infrastructure.database.opensearch.index_settings.person import person_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.common import get_document_mapping
from services.base.infrastructure.database.opensearch.index_settings.shared.utils import convert_dsl_mapping_to_dict
from services.base.infrastructure.database.opensearch.index_settings.sleep_v3 import sleep_mapping
from services.base.infrastructure.database.opensearch.index_settings.symptom import symptom_mapping
from services.base.infrastructure.database.opensearch.opensearch_data_labels import OS_LABEL_CATCH_ALL
from services.base.infrastructure.database.opensearch.opensearch_index_constants import TEMPLATE_INDEX, OpenSearchIndex
from settings.app_config import settings


def get_template_document_base_mapping() -> dict:
    return {
        DocumentLabels.TYPE: Keyword(),
        EventFields.DURATION: Double(),
        EventFields.NAME: Text(
            fields={"keyword": Keyword(ignore_above=EventValueLimits.MAX_NAME_LENGTH)}, copy_to=OS_LABEL_CATCH_ALL
        ),
        EventFields.CATEGORY: Keyword(copy_to=OS_LABEL_CATCH_ALL),
        DocumentLabels.TAGS: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL),
        EventFields.NOTE: Text(copy_to=OS_LABEL_CATCH_ALL),
    }


def get_template_mapping() -> Dict[str, Any]:
    return convert_dsl_mapping_to_dict(
        {
            TemplateFields.NAME: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL),
            TemplateFields.RBAC: Object(
                properties={
                    DocumentLabels.OWNER_ID: Keyword(),
                }
            ),
            TemplateFields.ARCHIVED_AT: Date(),
            TemplateFields.DOCUMENT: Object(
                properties=get_template_document_base_mapping()
                | activity_mapping
                | body_metric_mapping
                | content_mapping
                | exercise_mapping
                | nutrition_mapping
                | {
                    NutritionTemplateFields.AMOUNT_UNIT: Keyword(),
                    NutritionTemplateFields.AMOUNT: Double(),
                    NutritionTemplateFields.ITEMS_PER_SERVING: Double(),
                }
                | sleep_mapping
                | medication_mapping
                | symptom_mapping
                | note_mapping
                | person_mapping
            ),
            TemplateFields.DOCUMENT_NAME: Text(
                fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL
            ),
            TemplateFields.DOCUMENT_TYPE: Keyword(),
            TemplateFields.TEMPLATE_IDS: Keyword(),
            DocumentLabels.TAGS: Object(
                properties={
                    DocumentLabels.TAG: Text(fields={"keyword": Keyword(ignore_above=64)}, copy_to=OS_LABEL_CATCH_ALL)
                }
            ),
        }
        | get_document_mapping(),
        strict_mapping=True,
    )


def get_template_settings():
    return {
        "default_pipeline": None,
        "number_of_shards": settings.OS_PRIMARY_SHARDS_COUNT,
        "number_of_replicas": settings.OS_REPLICA_SHARDS_COUNT,
    }


TemplateIndexModel: OpenSearchIndex = OpenSearchIndex(
    name=TEMPLATE_INDEX,
    mappings=get_template_mapping(),
    settings=get_template_settings(),
    is_splittable=False,
)
