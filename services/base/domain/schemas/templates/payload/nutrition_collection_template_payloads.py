from abc import ABC
from dataclasses import dataclass
from typing import Literal

from pydantic import Field

from services.base.domain.annotated_types import NonEmptyStr, Rounded6Float
from services.base.domain.enums.data_types import DataType
from services.base.domain.enums.units.volume_unit import VolumeUnit
from services.base.domain.enums.units.weight_unit import WeightUnit
from services.base.domain.schemas.events.event import EventValueLimits
from services.base.domain.schemas.events.nutrition.drink import DrinkCategory, DrinkIdentifier
from services.base.domain.schemas.events.nutrition.food import FoodCategory, FoodIdentifier
from services.base.domain.schemas.events.nutrition.nutrients import Nutrients
from services.base.domain.schemas.events.nutrition.nutrition_collection import (
    NutritionFields,
    NutritionValueLimits,
)
from services.base.domain.schemas.events.nutrition.supplement import SupplementCategory, SupplementIdentifier
from services.base.domain.schemas.templates.payload.template_payload_base import TemplatePayloadBase


@dataclass(frozen=True)
class NutritionTemplateFields:
    AMOUNT_UNIT = "amount_unit"
    AMOUNT = "amount"
    ITEMS_PER_SERVING = "items_per_serving"


class NutritionTemplatePayloadBase(TemplatePayloadBase, ABC):
    brand: NonEmptyStr | None = Field(
        alias=NutritionFields.BRAND,
        max_length=NutritionValueLimits.MAX_BRAND_NAME,
    )
    rating: int | None = Field(
        alias=NutritionFields.RATING,
        ge=EventValueLimits.RATING_MINIMUM_VALUE,
        le=EventValueLimits.RATING_MAXIMUM_VALUE,
    )
    note: NonEmptyStr | None = Field(
        alias=NutritionFields.NOTE,
        max_length=EventValueLimits.MAX_NOTE_LENGTH,
    )
    items_per_serving: Rounded6Float | None = Field(
        alias=NutritionTemplateFields.ITEMS_PER_SERVING,
        ge=NutritionValueLimits.MIN_ITEMS_PER_SERVING,
        le=NutritionValueLimits.MAX_ITEMS_PER_SERVING,
    )
    consumed_amount: Rounded6Float = Field(
        alias=NutritionFields.CONSUMED_AMOUNT,
        ge=NutritionValueLimits.MIN_CONSUMED_AMOUNT,
        le=NutritionValueLimits.MAX_CONSUMED_AMOUNT,
    )
    amount: Rounded6Float = Field(
        alias=NutritionTemplateFields.AMOUNT,
        ge=NutritionValueLimits.MIN_AMOUNT,
        le=NutritionValueLimits.MAX_AMOUNT,
    )
    calories: Rounded6Float | None = Field(
        alias=NutritionFields.CALORIES,
        ge=NutritionValueLimits.MIN_CALORIES,
        le=NutritionValueLimits.MAX_CALORIES,
    )
    flavor: NonEmptyStr | None = Field(
        alias=NutritionFields.FLAVOR,
        max_length=NutritionValueLimits.MAX_FLAVOUR_LENGTH,
    )
    nutrients: Nutrients | None = Field(alias=NutritionFields.NUTRIENTS)


class DrinkTemplatePayload(NutritionTemplatePayloadBase, DrinkIdentifier):
    type: Literal[DataType.Drink] = Field(alias=NutritionFields.TYPE)
    category: DrinkCategory = Field(alias=NutritionFields.CATEGORY)
    consumed_type: VolumeUnit | Literal["item", "serving"] = Field(alias=NutritionFields.CONSUMED_TYPE)
    amount_unit: VolumeUnit = Field(
        alias=NutritionTemplateFields.AMOUNT_UNIT,
        max_length=NutritionValueLimits.MAX_UNIT_LENGTH,
    )


class FoodTemplatePayload(NutritionTemplatePayloadBase, FoodIdentifier):
    type: Literal[DataType.Food] = Field(alias=NutritionFields.TYPE)
    category: FoodCategory = Field(alias=NutritionFields.CATEGORY)
    consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"] = Field(alias=NutritionFields.CONSUMED_TYPE)
    amount_unit: WeightUnit | VolumeUnit = Field(
        alias=NutritionTemplateFields.AMOUNT_UNIT,
        max_length=NutritionValueLimits.MAX_UNIT_LENGTH,
    )


class SupplementTemplatePayload(NutritionTemplatePayloadBase, SupplementIdentifier):
    type: Literal[DataType.Supplement] = Field(alias=NutritionFields.TYPE)
    category: SupplementCategory = Field(alias=NutritionFields.CATEGORY)
    consumed_type: WeightUnit | VolumeUnit | Literal["item", "serving"] = Field(alias=NutritionFields.CONSUMED_TYPE)
    amount_unit: WeightUnit | VolumeUnit = Field(
        alias=NutritionTemplateFields.AMOUNT_UNIT,
        max_length=NutritionValueLimits.MAX_UNIT_LENGTH,
    )
