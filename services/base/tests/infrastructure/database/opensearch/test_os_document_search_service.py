from unittest.mock import AsyncMock, MagicMock

import pytest

from services.base.domain.schemas.diary_events import DiaryEvents
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.infrastructure.database.opensearch.opensearch_client import OpenSearchClient
from services.base.infrastructure.database.opensearch.os_document_search_service import OSDocumentSearchService


class TestOSDocumentSearchService:
    @pytest.fixture
    def mock_opensearch_client(self):
        client = MagicMock(spec=OpenSearchClient)
        client.search_indices = AsyncMock()
        return client

    @pytest.fixture
    def document_search_service(self, mock_opensearch_client):
        return OSDocumentSearchService(client=mock_opensearch_client)

    @pytest.mark.parametrize(
        "field_name,cardinality_value,expected_count",
        [
            pytest.param("user_id.keyword", 42, 42, id="basic_cardinality_count"),
            pytest.param("tags.tag.keyword", 0, 0, id="zero_cardinality_count"),
            pytest.param("metadata.organization.keyword", 1, 1, id="single_cardinality_count"),
        ],
    )
    async def test_count_unique_by_query_should_return_cardinality_value(
        self,
        document_search_service: OSDocumentSearchService,
        mock_opensearch_client: MagicMock,
        field_name: str,
        cardinality_value: int,
        expected_count: int,
    ):
        # Arrange
        query = Query(
            type_queries=[
                TypeQuery(domain_types=[DiaryEvents], query=ValuesQuery(field_name="type", values=["test_type"]))
            ]
        )

        mock_response = {"aggregations": {"cardinality_aggregation": {"value": cardinality_value}}}
        mock_opensearch_client.search_indices.return_value = mock_response

        # Act
        result = await document_search_service.count_unique_by_query(query=query, field_name=field_name)

        # Assert
        assert result == expected_count
        mock_opensearch_client.search_indices.assert_called_once()

        # Verify the request was built correctly
        call_args = mock_opensearch_client.search_indices.call_args
        request_body = call_args.kwargs["body"]

        # Check that cardinality aggregation was added
        assert "aggs" in request_body
        assert "cardinality_aggregation" in request_body["aggs"]
        assert request_body["aggs"]["cardinality_aggregation"]["cardinality"]["field"] == field_name

        # Check that size is 0 (we only want aggregation results)
        assert request_body["size"] == 0

    async def test_count_unique_by_query_should_handle_complex_query(
        self,
        document_search_service: OSDocumentSearchService,
        mock_opensearch_client: MagicMock,
    ):
        # Arrange
        query = Query(
            type_queries=[
                TypeQuery(
                    domain_types=[DiaryEvents], query=ValuesQuery(field_name="type", values=["exercise", "nutrition"])
                )
            ]
        )

        mock_response = {"aggregations": {"cardinality_aggregation": {"value": 15}}}
        mock_opensearch_client.search_indices.return_value = mock_response

        # Act
        result = await document_search_service.count_unique_by_query(query=query, field_name="user_id.keyword")

        # Assert
        assert result == 15
        mock_opensearch_client.search_indices.assert_called_once()

    async def test_count_unique_by_query_usage_example(
        self,
        document_search_service: OSDocumentSearchService,
        mock_opensearch_client: MagicMock,
    ):
        """
        Example usage: Count unique users who have created diary events of specific types
        This could be useful for analytics to understand user engagement patterns.
        """
        # Arrange - Query for exercise and nutrition events
        query = Query(
            type_queries=[
                TypeQuery(
                    domain_types=[DiaryEvents], query=ValuesQuery(field_name="type", values=["exercise", "nutrition"])
                )
            ]
        )

        mock_response = {
            "aggregations": {
                "cardinality_aggregation": {"value": 25}  # 25 unique users have created exercise or nutrition events
            }
        }
        mock_opensearch_client.search_indices.return_value = mock_response

        # Act - Count unique users
        unique_user_count = await document_search_service.count_unique_by_query(
            query=query, field_name="metadata.user_uuid.keyword"  # Count unique user UUIDs
        )

        # Assert
        assert unique_user_count == 25

        # Verify the aggregation was built correctly
        call_args = mock_opensearch_client.search_indices.call_args
        request_body = call_args.kwargs["body"]
        assert request_body["aggs"]["cardinality_aggregation"]["cardinality"]["field"] == "metadata.user_uuid.keyword"
