import logging
from datetime import datetime
from typing import Optional, Sequence

from opensearchpy import <PERSON><PERSON><PERSON><PERSON><PERSON>earch

from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.repository.member_user_device_repository import MemberUserDeviceRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.wrappers import (
    Range,
    ReadFromDatabaseWrapper,
)
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.query.boolean_query import AndQuery
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.infrastructure.database.opensearch.query_translator.query_translator import QueryTranslator


class StatsCollector:
    def __init__(
        self,
        member_user_repository: MemberUserRepository,
        device_user_repository: MemberUserDeviceRepository,
        client: AsyncOpenSearch,
    ):
        self._member_user_repository = member_user_repository
        self._device_user_repository = device_user_repository
        self._client = client

    async def get_users(
        self,
        user_type: str | None,
        time_gte: datetime | None,
        time_lte: datetime | None,
        field: str,
        user_ids: Sequence[UUID] | None,
    ) -> int:
        """
        Asynchronously retrieves the count of users based on the provided filters.

        Parameters:
        - user_type (Optional[str]): An optional filter to count users of a specific type.
          If not provided, users of all types are counted.
        - time_gte (Optional[datetime]): The start date for filtering users based on the specified field.
          If not provided, no lower bound is applied.
        - time_lte (Optional[datetime]): The end date for filtering users based on the specified field.
          If not provided, no upper bound is applied.
        - field (str): The field to filter users by date. Default is 'created_at'. Other fields like
          'last_logged_at' can also be used.
        - uuid_filter (Optional[list[str]]): A list of user UUIDs to filter by. If not provided, no UUID filtering is applied.

        Returns:
        - int: The count of users that match the provided filters. If no filters are provided, returns the total number of users.
        """
        # Set the range filter
        if any([time_gte, time_lte]):
            range_filter = Range(
                field_name=field,
                start_date=time_gte if time_gte else None,
                end_date=time_lte if time_lte else None,
            )
        else:
            range_filter = None

        search_keys = {
            **({"user_uuid": user_ids} if user_ids else {}),
            **({"type": user_type} if user_type else {}),
        }

        users_count = await self._member_user_repository.count(
            wrapper=ReadFromDatabaseWrapper(search_keys=search_keys, range_filter=range_filter, count=True)
        )

        return users_count

    async def count_users_with_documents(
        self,
        data_schema: type[Document],
        time_gte: datetime,
        time_lte: datetime,
        uuid_filter: list[str] = None,
    ) -> int:
        query = CommonLeafQueries.timestamp_range_query(gte=time_gte, lte=time_lte)
        if uuid_filter:
            values_query = ValuesQuery(
                field_name=f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}",
                values=uuid_filter,
            )
            query = AndQuery(queries=[values_query, query])

        query = Query(type_queries=[TypeQuery(query=query, domain_types=[data_schema])])
        query_result = QueryTranslator.translate(query)

        query = {
            **query_result.query_as_dict,
            "aggs": {"unique_users": {"cardinality": {"field": "metadata.user_uuid"}}},
        }

        try:
            response = await self._client.search(index=query_result.indices, body=query)
            return response["aggregations"]["unique_users"]["value"]
        except Exception as e:
            logging.error(f"An error occurred during the search operation: {e}")
            return 0

    async def get_multiple_device_users(self) -> int:
        """
        Returns a number of users that use multiple devices for one account
        """
        unique_users = await self._device_user_repository.count(
            wrapper=ReadFromDatabaseWrapper(search_keys={}, count=True, count_unique_field="user_uuid"),
        )
        count_of_device_entities = await self._device_user_repository.count(
            wrapper=ReadFromDatabaseWrapper(
                search_keys={},
                count=True,
            ),
        )
        return count_of_device_entities - unique_users
