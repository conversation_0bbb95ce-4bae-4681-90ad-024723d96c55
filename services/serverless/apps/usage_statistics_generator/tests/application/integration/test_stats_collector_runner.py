import random
from datetime import datetime, timed<PERSON>ta
from typing import Any, Async<PERSON>enerator, Awaitable, Callable, Sequence
from uuid import UUID

import pytest

from services.base.application.boundaries.time_input import TimeRangeInput
from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.application.database.models.filter_types import UserUUIDTermsFilter
from services.base.application.database.models.filters import Filters
from services.base.application.generators.primitive_types_generator import PrimitiveTypesGenerator
from services.base.domain.enums.member_user_role import MemberUserType
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.domain.schemas.shared_v2 import DeprEventModel
from services.base.domain.schemas.usage_statistics_output import UsageStatisticsResultEntity
from services.base.tests.domain.builders.emotion_builder import EmotionBuilder
from services.base.tests.domain.builders.event_builder import <PERSON><PERSON>uilder
from services.base.tests.domain.builders.heart_rate_builder import <PERSON>Rate<PERSON>uilder
from services.base.tests.domain.builders.note_builder import NoteBuilder
from services.base.tests.domain.builders.stress_builder import StressBuilder
from services.serverless.apps.usage_statistics_generator.application.collect_usage_statistics.application.stats_collector import (
    StatsCollector,
)
from services.serverless.apps.usage_statistics_generator.application.collect_usage_statistics.application.stats_collector_runner import (
    StatsCollectorRunner,
)


async def _delete_user_documents(
    user_uuid: UUID, data_schema: type[DeprEventModel], event_repo: DeprEventRepository
) -> None:
    filters = Filters()
    filters.must_filters.with_filters([UserUUIDTermsFilter(value=[str(user_uuid)])])
    await event_repo.delete_by_query(data_schema=data_schema, user_uuid=user_uuid, filters=filters)


@pytest.fixture
async def expected_usage_data(
    depr_event_repo: DeprEventRepository,
    event_repo: EventRepository,
    user_factory: Callable[[MemberUserType, datetime], Awaitable[MemberUser]],
) -> AsyncGenerator[tuple[TimeRangeInput, list[Any], UsageStatisticsResultEntity], Any]:
    inserted_notes = []
    inserted_events = []
    standard_users = []
    anonymous_users = []
    stale_users = []
    time_lte = PrimitiveTypesGenerator.generate_random_aware_datetime()
    time_gte = time_lte - timedelta(days=7)
    time_range = TimeRangeInput(time_gte=time_gte, time_lte=time_lte)

    for i in range(random.randint(2, 4)):
        standard_user: MemberUser = await user_factory(MemberUserType.STANDARD, time_lte)
        anonymous_user: MemberUser = await user_factory(MemberUserType.ANONYMOUS, time_lte)
        stale_user: MemberUser = await user_factory(MemberUserType.STANDARD, time_lte - timedelta(days=200))
        stress = [
            StressBuilder()
            .with_owner_id(owner_id=standard_user.user_uuid)
            .with_timestamp(PrimitiveTypesGenerator.generate_random_aware_datetime(gte=time_gte, lte=time_lte))
            .build()
            for _ in range(random.randint(1, 5))
        ]
        emotion = [
            EmotionBuilder()
            .with_owner_id(owner_id=standard_user.user_uuid)
            .with_timestamp(PrimitiveTypesGenerator.generate_random_aware_datetime(gte=time_gte, lte=time_lte))
            .build()
            for _ in range(random.randint(1, 5))
        ]
        events = [
            EventBuilder()
            .with_owner_id(owner_id=standard_user.user_uuid)
            .with_timestamp(PrimitiveTypesGenerator.generate_random_aware_datetime(gte=time_gte, lte=time_lte))
            .build()
            for _ in range(random.randint(1, 5))
        ]
        heart_rate = [
            HeartRateBuilder()
            .with_user_uuid(user_uuid=standard_user.user_uuid)
            .with_timestamp(PrimitiveTypesGenerator.generate_random_aware_datetime(gte=time_gte, lte=time_lte))
            .build()
            for _ in range(random.randint(1, 5))
        ]
        notes = [
            NoteBuilder()
            .with_owner_id(owner_id=standard_user.user_uuid)
            .with_timestamp(PrimitiveTypesGenerator.generate_random_aware_datetime(gte=time_gte, lte=time_lte))
            .build()
            for _ in range(random.randint(1, 5))
        ]
        inserted_events = await event_repo.insert(stress + emotion + events, force_strong_consistency=True)
        _ = await depr_event_repo.insert(models=heart_rate, force_strong_consistency=True)

        inserted_notes = await event_repo.insert(events=notes, force_strong_consistency=True)

        standard_users.append(standard_user)
        anonymous_users.append(anonymous_user)
        stale_users.append(stale_user)

    testable_users = standard_users + anonymous_users + stale_users

    yield (
        time_range,
        testable_users,
        UsageStatisticsResultEntity(
            new_users=len(standard_users) + len(anonymous_users),
            new_anonymous_users=len(anonymous_users),
            total_users=len(standard_users) + len(anonymous_users) + len(stale_users),
            total_anonymous_users=len(anonymous_users),
            active_users=len(standard_users) + len(anonymous_users),
            active_anonymous_users=len(anonymous_users),
            stale_users=len(stale_users),
            multiple_device_users=0,
            users_with_notes=len(standard_users),
            users_with_events=len(standard_users),
            users_with_rating=len(standard_users),
            users_with_location=0,
            users_with_heartrate=len(standard_users),
            document_count_csv_string="",  # Not able to reconstruct the document count csv left empty
        ),
    )

    # Teardown
    await event_repo.delete_by_id(ids=[e.id for e in inserted_events])
    for user in standard_users:
        await event_repo.delete_by_id(ids=[note.id for note in inserted_notes])
        await _delete_user_documents(user_uuid=user.user_uuid, data_schema=HeartRate, event_repo=depr_event_repo)


async def test_stats_collector_on_random_data_should_pass(
    collector: StatsCollector,
    search_service: DocumentSearchService,
    expected_usage_data: tuple[TimeRangeInput, Sequence[MemberUser], UsageStatisticsResultEntity],
):
    time_range, user_list, expected_usage_result = expected_usage_data
    testable_uuids = [user.user_uuid for user in user_list]

    stats_runner = StatsCollectorRunner(
        collector=collector,
        search_service=search_service,
    )

    result = await stats_runner.execute_async(
        time_gte=time_range.time_gte,
        time_lte=time_range.time_lte,
        user_ids=testable_uuids,
    )

    assert isinstance(result, UsageStatisticsResultEntity)
    assert result.active_users == expected_usage_result.active_users
    assert result.active_anonymous_users == expected_usage_result.active_anonymous_users
    assert result.total_users == expected_usage_result.total_users
    assert result.total_anonymous_users == expected_usage_result.total_anonymous_users
    assert result.new_users == expected_usage_result.new_users
    assert result.new_anonymous_users == expected_usage_result.new_anonymous_users
    assert result.stale_users == expected_usage_result.stale_users
    assert result.users_with_rating == expected_usage_result.users_with_rating
    assert result.users_with_events == expected_usage_result.users_with_events
    assert result.users_with_heartrate == expected_usage_result.users_with_heartrate
    assert result.users_with_notes == expected_usage_result.users_with_notes
