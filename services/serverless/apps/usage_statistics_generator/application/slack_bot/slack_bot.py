import logging
from io import By<PERSON><PERSON>

from slack_sdk.web.async_client import Async<PERSON>eb<PERSON>lient
from slack_sdk.web.async_slack_response import AsyncSlackResponse


class SlackBotError(Exception):
    pass


class SlackBot:
    def __init__(self, client: AsyncWebClient):
        self._client = client

    async def post_message(self, message: str, channel_id: str) -> AsyncSlackResponse:
        try:
            response = await self._client.chat_postMessage(channel=channel_id, text=message)
            if not response:
                raise SlackBotError("Received None response from Slack API")

            message_data = response.get("message")
            if not message_data:
                raise SlackBotError("Response missing message data")

            posted_text = message_data.get("text")
            if posted_text != message:
                raise SlackBotError(f"Message text mismatch. Expected: '{message}', Got: '{posted_text}'")

            logging.info(f"Message posted to channel {channel_id}")
            return response

        except Exception as e:
            raise SlackBotError(f"Failed to post message: {e}") from e

    async def join_channel(self, channel_id: str) -> None:
        try:
            auth_response = await self._client.auth_test()
            if not auth_response:
                raise SlackBotError("Received None response from auth_test")

            bot_user_id = auth_response.get("user_id")
            if not bot_user_id:
                raise SlackBotError("auth_test response missing user_id")

            response = await self._client.conversations_members(channel=channel_id)
            if not response:
                raise SlackBotError("Received None response from conversations_members")

            members = response.get("members", [])
            if bot_user_id in members:
                logging.info(f"Bot already in channel {channel_id}")
                return

            join_response = await self._client.conversations_join(channel=channel_id)
            if not join_response:
                raise SlackBotError("Received None response from conversations_join")

            if not join_response.get("ok"):
                raise SlackBotError(f"Failed to join channel {channel_id}")

            logging.info(f"Bot joined channel {channel_id}")

        except Exception as e:
            raise SlackBotError(f"Failed to join channel: {e}") from e

    async def upload_csv_with_message(
        self, channel_id: str, csv_byte_array: BytesIO, message: str, filename: str
    ) -> AsyncSlackResponse:
        try:
            response = await self._client.files_upload_v2(
                channel=channel_id,
                file=csv_byte_array,
                filename=filename,
                title="Uploaded CSV",
                initial_comment=message,
            )

            if not response:
                raise SlackBotError("Received None response from files_upload_v2")

            if not response.get("ok"):
                raise SlackBotError(f"Failed to upload file to channel {channel_id}")

            logging.info(f"File uploaded to channel {channel_id}")
            return response

        except Exception as e:
            raise SlackBotError(f"Failed to upload file: {e}") from e

    async def get_last_messages(self, channel_id: str, count: int) -> list[dict]:
        try:
            response = await self._client.conversations_history(channel=channel_id, limit=count)
            if not response:
                raise SlackBotError("Received None response from conversations_history")

            messages = response.get("messages", [])
            logging.info(f"Retrieved {len(messages)} messages from channel {channel_id}")
            return messages

        except Exception as e:
            raise SlackBotError(f"Failed to retrieve messages: {e}") from e

    async def delete_message(self, channel_id: str, message_ts: str) -> None:
        try:
            response = await self._client.chat_delete(channel=channel_id, ts=message_ts)
            if not response:
                raise SlackBotError("Received None response from chat_delete")

            if not response.get("ok"):
                raise SlackBotError(f"Failed to delete message {message_ts}")

            logging.info(f"Message {message_ts} deleted from channel {channel_id}")

        except Exception as e:
            raise SlackBotError(f"Failed to delete message: {e}") from e
