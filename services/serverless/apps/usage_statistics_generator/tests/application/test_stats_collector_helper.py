import pytest

from services.serverless.apps.usage_statistics_generator.application.collect_usage_statistics.application.stats_collector_helpers import (
    get_documents_data_into_df,
)


async def test_get_document_data_into_df_should_pass(
    events_sample,
):
    document_count_df = await get_documents_data_into_df(
        documents=events_sample,
        fields=["name", "metadata.user_uuid"],
    )
    assert len(document_count_df.name) == 10
    assert document_count_df.name[0] == "name 0"  # assert the first event name is loaded correctly
    assert document_count_df.columns.to_list() == [
        "name",
        "user_uuid",
    ]  # assert that both the fields are converted into columns of dataframe


async def test_get_document_data_into_df_fake_field_should_fail(
    events_sample,
):
    with pytest.raises(AttributeError):
        await get_documents_data_into_df(
            documents=events_sample,
            fields=["name", "metadata.user_uuid", "fake field"],
        )
