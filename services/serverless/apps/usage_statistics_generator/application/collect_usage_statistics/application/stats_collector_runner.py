# COLLECT
from datetime import datetime, timedelta
from typing import Sequence
from uuid import UUID

import pandas as pd

from services.base.application.database.document_search_service import DocumentSearchService
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.enums.event_type import EventType
from services.base.domain.enums.member_user_role import MemberUserType
from services.base.domain.enums.metadata import Organization
from services.base.domain.schemas.events.document_base import Document
from services.base.domain.schemas.heart_rate import HeartRate
from services.base.domain.schemas.location import Location
from services.base.domain.schemas.query.boolean_query import AndQuery
from services.base.domain.schemas.query.builders.common_leaf_queries import CommonLeafQueries
from services.base.domain.schemas.query.leaf_query import ValuesQuery
from services.base.domain.schemas.query.query import Query
from services.base.domain.schemas.query.type_query import TypeQuery
from services.base.domain.schemas.usage_statistics_output import (
    UsageStatisticsResultEntity,
)
from services.base.type_resolver import TypeResolver
from services.serverless.apps.usage_statistics_generator.application.collect_usage_statistics.application.stats_collector import (
    StatsCollector,
)


class StatsCollectorRunner:
    def __init__(
        self,
        collector: StatsCollector,
        search_service: DocumentSearchService,
    ):
        self._collector = collector
        self._search_service = search_service

    async def execute_async(
        self,
        time_gte: datetime,
        time_lte: datetime,
        user_ids: Sequence[UUID] | None = None,
    ) -> UsageStatisticsResultEntity:
        document_count_df = await self._create_document_count_df(
            datetime_range_gte=time_gte, datetime_range_lte=time_lte
        )

        # Gets information on users, active users over timeframe, new users registered over timeframe and total users at
        # the end of timeframe
        active_users = await self._collector.get_users(
            time_gte=time_gte,
            time_lte=time_lte,
            field="last_logged_at",
            user_ids=user_ids,
            user_type=None,
        )
        active_anonymous_user = await self._collector.get_users(
            time_gte=time_gte,
            time_lte=time_lte,
            field="last_logged_at",
            user_type=MemberUserType.ANONYMOUS,
            user_ids=user_ids,
        )
        new_users = await self._collector.get_users(
            time_gte=time_gte,
            time_lte=time_lte,
            user_ids=user_ids,
            user_type=None,
            field="created_at",
        )
        new_anonymous_users = await self._collector.get_users(
            time_gte=time_gte,
            time_lte=time_lte,
            user_type=MemberUserType.ANONYMOUS,
            user_ids=user_ids,
            field="created_at",
        )
        total_users = await self._collector.get_users(
            user_ids=user_ids,
            user_type=None,
            field="created_at",
            time_gte=None,
            time_lte=None,
        )
        total_anonymous_users = await self._collector.get_users(
            user_type=MemberUserType.ANONYMOUS,
            user_ids=user_ids,
            field="created_at",
            time_gte=None,
            time_lte=None,
        )
        stale_users = await self._collector.get_users(
            time_lte=time_gte - timedelta(days=180),
            field="last_logged_at",
            user_ids=user_ids,
            user_type=None,
            time_gte=None,
        )
        multiple_device_users = await self._collector.get_multiple_device_users()

        users_with_location = await self._count_users_with_documents(
            domain_type=Location,
            time_gte=time_gte,
            time_lte=time_lte,
            user_ids=user_ids,
        )
        users_with_heartrate = await self._count_users_with_documents(
            domain_type=HeartRate,
            time_gte=time_gte,
            time_lte=time_lte,
            user_ids=user_ids,
        )

        users_with_events: dict[EventType, int] = {}
        for event_type in EventType:
            users_with_events[event_type] = await self._count_users_with_documents(
                domain_type=event_type.to_domain_model(),
                time_gte=time_gte,
                time_lte=time_lte,
                user_ids=user_ids,
            )

        return UsageStatisticsResultEntity(
            new_users=new_users,
            new_anonymous_users=new_anonymous_users,
            total_users=total_users,
            total_anonymous_users=total_anonymous_users,
            active_users=active_users,
            active_anonymous_users=active_anonymous_user,
            stale_users=stale_users,
            multiple_device_users=multiple_device_users,
            users_with_events=users_with_events,
            users_with_location=users_with_location,
            users_with_heartrate=users_with_heartrate,
            document_count_csv_string=document_count_df.to_csv(index=False),
        )

    async def _create_document_count_df(
        self, datetime_range_gte: datetime, datetime_range_lte: datetime
    ) -> pd.DataFrame:
        organizations = [organization for organization in Organization]
        timestamp_range_query = CommonLeafQueries.timestamp_range_query(gte=datetime_range_gte, lte=datetime_range_lte)

        data = []
        for event_type in EventType:
            count_dict = {"DataType": event_type.name}
            for organization in organizations:
                organization_query = CommonLeafQueries.organization_values_query(organizations=[organization])
                query = Query(
                    type_queries=[
                        TypeQuery(
                            query=AndQuery(queries=[timestamp_range_query, organization_query]),
                            domain_types=[event_type.to_domain_model()],
                        )
                    ]
                )
                count = await self._search_service.count_by_query(query=query)
                count_dict[organization.name] = count

            count_dict["Total Documents"] = sum(count_dict[organization.name] for organization in organizations)
            data.append(count_dict)

        df = pd.DataFrame(data)

        total_row = {"DataType": "TotalDocuments"}
        for organization in organizations:
            total_row[organization.name] = df[organization.name].sum()

        total_row["Total Documents"] = df["Total Documents"].sum()
        total_df = pd.DataFrame([total_row])

        df = pd.concat([df, total_df], ignore_index=True)
        return df

    async def _count_users_with_documents(
        self, domain_type: type[Document], time_gte: datetime, time_lte: datetime, user_ids: Sequence[UUID] | None
    ) -> int:
        timestamp_range_query = CommonLeafQueries.timestamp_range_query(gte=time_gte, lte=time_lte)
        queries = []
        if user_ids:
            values_query = ValuesQuery(
                field_name=f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}",
                values=[str(uuid) for uuid in user_ids],
            )
            queries.append(values_query)
        queries.append(timestamp_range_query)
        q = Query(type_queries=[TypeQuery(domain_types=[domain_type], query=AndQuery(queries=queries))])
        field = (
            f"{DocumentLabels.RBAC}.{DocumentLabels.OWNER_ID}.keyword"
            if issubclass(domain_type, tuple(TypeResolver.DOCUMENTS_V3))
            else f"{DocumentLabels.METADATA}.{DocumentLabels.USER_UUID}"
        )
        return await self._search_service.count_unique_by_query(
            query=q,
            field_name=field,
        )
