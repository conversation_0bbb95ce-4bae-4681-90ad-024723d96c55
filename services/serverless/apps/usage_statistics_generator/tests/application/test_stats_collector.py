import random
import uuid
from datetime import datetime, timedelta, timezone
from typing import Sequence

from services.base.domain.enums.member_user_role import MemberUserType
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.schemas.member_user.member_user import MemberUser


async def _generate_users(
    user_repository: MemberUserRepository,
    count: int,
    created_at: datetime,
    last_logged_at: datetime,
    user_type: MemberUserType = MemberUserType.STANDARD,
) -> Sequence[MemberUser]:
    users = []
    for i in range(count):
        user = user_repository.insert_or_update(
            MemberUser(
                user_uuid=uuid.uuid4(),
                first_name=f"fake_{i}",
                last_name=f"faker_{i}",
                created_at=created_at,
                last_logged_at=last_logged_at,
                type=user_type,
            )
        )
        assert user
        users.append(user)
    return users


async def _delete_users(users: Sequence[MemberUser], user_repository: MemberUserRepository):
    for user in users:
        await user_repository.delete(user=user)


async def test_get_total_users_on_site_valid_should_pass(collector):
    total_users = await collector.get_users()
    assert total_users >= 4  # 4 users loaded from seed data


async def test_get_total_anonymous_users_on_site_valid_should_pass(collector):
    anonymous_user_generated = 0
    generated_users = []
    for i in range(random.randint(2, 5)):
        user_type = random.choice([MemberUserType.ANONYMOUS, MemberUserType.STANDARD])
        users_to_generate = random.randint(2, 5)
        users = await _generate_users(
            user_repository=collector._member_user_repository,
            count=users_to_generate,
            created_at=datetime.now(timezone.utc) - timedelta(hours=1),
            last_logged_at=datetime.now(timezone.utc),
            user_type=user_type,
        )
        if user_type == MemberUserType.ANONYMOUS:
            anonymous_user_generated += len(users)
        generated_users.extend(users)

    total_anonymous_users = await collector.get_users(
        time_gte=datetime.now(timezone.utc) - timedelta(hours=24),
        user_type=MemberUserType.ANONYMOUS,
    )
    assert total_anonymous_users == anonymous_user_generated

    await _delete_users(generated_users, collector._member_user_repository)


async def test_get_new_users_on_site_valid_should_pass(collector):
    users = await _generate_users(
        user_repository=collector._member_user_repository,
        count=4,
        created_at=datetime.now(timezone.utc) - timedelta(hours=1),
        last_logged_at=datetime.now(timezone.utc),
    )
    total_users = await collector.get_users(
        time_gte=datetime.now(timezone.utc) - timedelta(hours=24), field="created_at"
    )
    assert total_users >= 4  # 4 users loaded from seed data

    await _delete_users(users, collector._member_user_repository)


async def test_get_active_users_valid_should_pass(collector):
    users = await _generate_users(
        user_repository=collector._member_user_repository,
        count=4,
        created_at=datetime.now(timezone.utc) - timedelta(hours=1),
        last_logged_at=datetime.now(timezone.utc),
    )
    total_users = await collector.get_users(
        time_gte=datetime.now(timezone.utc) - timedelta(hours=24), field="last_logged_at"
    )
    assert total_users >= 4  # 4 users loaded from seed data

    await _delete_users(users=users, user_repository=collector._member_user_repository)


async def test_get_multiple_device_users_valid_should_pass(collector):
    devices = await collector.get_multiple_device_users()
    assert devices == 0  # no devices registered through seed data
