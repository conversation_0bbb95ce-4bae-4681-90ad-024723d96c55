from typing import List, Optional, <PERSON><PERSON>
from zoneinfo import ZoneInfo

import pytest

from services.base.application.database.models.sorts import Sort, SortOrder
from services.base.domain.constants.document_labels import DocumentLabels
from services.base.domain.constants.time_constants import DEFAULT_TIMEZONE_UTC
from services.base.domain.schemas.events.feeling.emotion import EmotionFields
from services.base.domain.schemas.query.aggregations import AggregationMethod
from services.base.infrastructure.database.opensearch.opensearch_aggs_builder import OpenSearchAggsBuilder


@pytest.mark.parametrize(
    "input_tuple,expected_agg",
    [
        (
            ("test", 100, [Sort(name="test", order=SortOrder.DESCENDING)]),
            {
                "aggregation": {
                    "terms": {"field": "test", "size": 100},
                    "aggs": {
                        "sub_aggregation": {
                            "top_hits": {"_source": True, "size": 1, "sort": [{"test": {"order": "desc"}}]}
                        }
                    },
                }
            },
        ),
        (
            ("test2", 1000, None),
            {
                "aggregation": {
                    "terms": {"field": "test2", "size": 1000},
                    "aggs": {"sub_aggregation": {"top_hits": {"_source": True, "size": 1}}},
                }
            },
        ),
    ],
)
def test_opensearch_unique_aggregation_should_match_expected(
    input_tuple: Tuple[str, int, Optional[List[Sort]]], expected_agg: dict
):
    aggregation = (
        OpenSearchAggsBuilder()
        .with_unique_aggregation(field_name=input_tuple[0], size=input_tuple[1], sort=input_tuple[2])
        .build()
    )

    assert aggregation == expected_agg


@pytest.mark.parametrize(
    "input_tuple,expected_agg",
    [
        (
            ("test", 100),
            {"aggregation": {"terms": {"field": "test", "size": 100}}},
        ),
        (
            ("test2", 1000),
            {"aggregation": {"terms": {"field": "test2", "size": 1000}}},
        ),
    ],
)
def test_opensearch_frequency_distribution_agg_should_match_expected(input_tuple: Tuple[str, int], expected_agg: dict):
    aggregation = (
        OpenSearchAggsBuilder().with_frequency_distribution_agg(field_name=input_tuple[0], size=input_tuple[1]).build()
    )

    assert aggregation == expected_agg


@pytest.mark.parametrize(
    "field_name,expected_agg",
    [
        pytest.param(
            "test_field",
            {"cardinality_aggregation": {"cardinality": {"field": "test_field"}}},
            id="basic_cardinality_aggregation",
        ),
        pytest.param(
            "user_id.keyword",
            {"cardinality_aggregation": {"cardinality": {"field": "user_id.keyword"}}},
            id="keyword_field_cardinality_aggregation",
        ),
    ],
)
def test_opensearch_cardinality_aggregation_should_match_expected(field_name: str, expected_agg: dict):
    aggregation = OpenSearchAggsBuilder().with_cardinality_aggregation(field_name=field_name).build()

    assert aggregation == expected_agg


@pytest.mark.parametrize(
    "inp,expected_agg",
    [
        (
            {
                "field": DocumentLabels.TIMESTAMP,
                "interval": "1d",
                "histogram_field_aggregations": {
                    f"{EmotionFields.RATING}:{AggregationMethod.SUM.value}": {
                        AggregationMethod.SUM.value: {"field": EmotionFields.RATING}
                    },
                    f"{EmotionFields.DURATION}:{AggregationMethod.MAX.value}": {
                        AggregationMethod.MAX.value: {"field": EmotionFields.DURATION}
                    },
                },
                "timezone": DEFAULT_TIMEZONE_UTC,
            },
            {
                "requested_histogram": {
                    "date_histogram": {"field": "timestamp", "calendar_interval": "1d", "time_zone": "+00:00"},
                    "aggs": {
                        "rating:sum": {"sum": {"field": "rating"}},
                        "duration:max": {"max": {"field": "duration"}},
                    },
                }
            },
        ),
        (
            {
                "field": DocumentLabels.TIMESTAMP,
                "interval": "1h",
                "histogram_field_aggregations": {
                    f"{EmotionFields.RATING}:{AggregationMethod.SUM.value}": {
                        AggregationMethod.SUM.value: {"field": EmotionFields.RATING}
                    },
                    f"{EmotionFields.RATING}:{AggregationMethod.MAX.value}": {
                        AggregationMethod.MAX.value: {"field": EmotionFields.RATING}
                    },
                    f"{EmotionFields.RATING}:{AggregationMethod.MIN.value}": {
                        AggregationMethod.MIN.value: {"field": EmotionFields.RATING}
                    },
                    f"{EmotionFields.RATING}:{AggregationMethod.AVG.value}": {
                        AggregationMethod.AVG.value: {"field": EmotionFields.RATING}
                    },
                },
                "timezone": ZoneInfo(
                    "Africa/Johannesburg"
                ),  # We are using this zone info to avoid DST offsets making this test flaky, Johannesburg is in the UTC+2 timezone all year round
            },
            {
                "requested_histogram": {
                    "date_histogram": {"field": "timestamp", "calendar_interval": "1h", "time_zone": "+02:00"},
                    "aggs": {
                        "rating:sum": {"sum": {"field": "rating"}},
                        "rating:avg": {"avg": {"field": "rating"}},
                        "rating:min": {"min": {"field": "rating"}},
                        "rating:max": {"max": {"field": "rating"}},
                    },
                }
            },
        ),
        (
            {
                "field": DocumentLabels.TIMESTAMP,
                "interval": "1d",
                "histogram_field_aggregations": None,
                "timezone": DEFAULT_TIMEZONE_UTC,
            },
            {
                "requested_histogram": {
                    "date_histogram": {"field": "timestamp", "calendar_interval": "1d", "time_zone": "+00:00"},
                }
            },
        ),
    ],
)
def test_opensearch_date_histogram_agg_should_match_expected(inp: dict, expected_agg: dict):
    aggregation = (
        OpenSearchAggsBuilder()
        .with_date_histogram_agg(
            field=inp["field"],
            histogram_field_aggregations=inp["histogram_field_aggregations"],
            interval=inp["interval"],
            timezone=inp["timezone"],
        )
        .build()
    )

    assert aggregation == expected_agg
