# For some reason the loop is closed
# This overrides the default behaviour of the loop in pytest-asyncio
import asyncio
import os
from datetime import datetime, timezone
from typing import Any, AsyncGenerator, Awaitable, List, Protocol

import pytest

from services.base.application.database.depr_event_repository import DeprEventRepository
from services.base.application.database.document_search_service import DocumentSearchService
from services.base.domain.enums.member_user_role import MemberUserType
from services.base.domain.repository.event_repository import EventRepository
from services.base.domain.repository.member_user_repository import MemberUserRepository
from services.base.domain.repository.usage_stats_repository import UsageStatsRepository
from services.base.domain.schemas.member_user.member_user import MemberUser
from services.base.tests.domain.builders.member_user_builder import MemberUserBuilder
from services.serverless.apps.usage_statistics_generator.application.slack_bot.slack_bot import SlackBot
from services.serverless.apps.usage_statistics_generator.application.usage_statistics_use_case import (
    UsageStatisticsUseCase,
)
from services.serverless.apps.usage_statistics_generator.dependency_bootstrapper import DependencyBootstrapper


def pytest_collection_modifyitems(config, items):
    # Get the value of the environment variable and command-line option
    pipeline_run = os.getenv("PIPELINE_RUN")
    marks_option = config.getoption("-m", "")
    non_skipped_tests = []

    if pipeline_run:
        if "integration" not in marks_option:
            skip_integration = pytest.mark.skip(reason="skipped because it is an integration test")
            for item in items:
                if any(marker.name == "integration" for marker in item.iter_markers()):
                    item.add_marker(skip_integration)
                else:
                    non_skipped_tests.append(item)
        else:
            skip_non_integration = pytest.mark.skip(reason="skipped because it not an integration test")
            for item in items:
                if not any(marker.name == "integration" for marker in item.iter_markers()):
                    item.add_marker(skip_non_integration)
                else:
                    non_skipped_tests.append(item)


@pytest.fixture(scope="session")
async def dependency_bootstrapper() -> AsyncGenerator[DependencyBootstrapper, Any]:
    bootstrapper = DependencyBootstrapper().build()
    yield bootstrapper
    await bootstrapper.cleanup()


@pytest.fixture(scope="session")
def depr_event_repo(dependency_bootstrapper: DependencyBootstrapper) -> DeprEventRepository:
    return dependency_bootstrapper.get(interface=DeprEventRepository)


@pytest.fixture(scope="session")
def event_repo(dependency_bootstrapper: DependencyBootstrapper) -> EventRepository:
    return dependency_bootstrapper.get(interface=EventRepository)


@pytest.fixture(scope="session")
def usage_stats_use_case(dependency_bootstrapper: DependencyBootstrapper) -> UsageStatisticsUseCase:
    return dependency_bootstrapper.get(interface=UsageStatisticsUseCase)


@pytest.fixture(scope="session")
def search_service(dependency_bootstrapper: DependencyBootstrapper) -> DocumentSearchService:
    return dependency_bootstrapper.get(interface=DocumentSearchService)


@pytest.fixture(scope="session")
def usage_stats_repository(dependency_bootstrapper: DependencyBootstrapper) -> UsageStatsRepository:
    return dependency_bootstrapper.get(interface=UsageStatsRepository)


@pytest.fixture(scope="session")
async def member_user_repository(dependency_bootstrapper: DependencyBootstrapper) -> MemberUserRepository:
    return dependency_bootstrapper.get(interface=MemberUserRepository)


@pytest.fixture(scope="module")
def cost_notification_slack_bot(dependency_bootstrapper: DependencyBootstrapper) -> SlackBot:
    return dependency_bootstrapper.get(interface=SlackBot)


class UserFactoryCallable(Protocol):
    def __call__(
        self,
        user_type: MemberUserType = MemberUserType.STANDARD,
        created_at: datetime = datetime.now(timezone.utc),
    ) -> Awaitable[MemberUser]: ...


@pytest.fixture(scope="function")
async def user_factory(member_user_repository: MemberUserRepository) -> AsyncGenerator[UserFactoryCallable, None]:
    created_users: List[MemberUser] = []

    async def create_user(
        user_type: MemberUserType = MemberUserType.STANDARD, created_at: datetime = datetime.now(timezone.utc)
    ) -> MemberUser:
        user = (
            MemberUserBuilder()
            .with_type(user_type=user_type)
            .with_created_at(created_at)
            .with_last_logged_at(created_at)
            .build()
        )
        user = await member_user_repository.insert_or_update(user)
        assert user
        created_users.append(user)
        return user

    yield create_user

    # Cleanup code: delete all created users
    for user_to_delete in created_users:
        await member_user_repository.delete(user_to_delete)


@pytest.fixture(scope="session")
def event_loop():
    try:
        loop = asyncio.get_running_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
    yield loop
    loop.close()
